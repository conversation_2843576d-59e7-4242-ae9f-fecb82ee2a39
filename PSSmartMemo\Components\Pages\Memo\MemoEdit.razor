@page "/memos/log/{strMemoLogId}"
@inject NavigationManager NavMgr
@inject CorporateService CorpService
@inject MemoDataService Service
@inject WorklistDataService WLService
@inject IJSRuntime js
@inject AdminDataService AdminService
@inject AppDataService AppDataService
@using System.Text.RegularExpressions
@using ButtonType = MudBlazor.ButtonType
@using FileInfo = System.IO.FileInfo
@rendermode InteractiveServer
@inject IWebHostEnvironment Env
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Path = Path
@using Microsoft.AspNetCore.Components.Authorization

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url="/memos"></BreadcrumbItem>
        <BreadcrumbItem Text="Edit Memo" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<SfDialog @ref="dlgFileUpload" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Visible="false">
    <DialogTemplates>
        <Header>Upload Attachment</Header>
        <Content>
            <EditForm Model="@newAttachment" OnValidSubmit="@(async () => await HandleFileUpload())"
                      FormName="AttachmentForm">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="row mb-3">
                    <div class="col-md">
                        <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                       Accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
                                       FilesChanged="FileSelected"
                                       MaximumFileCount="1000">
                            <ActivatorContent>
                                <MudButton HtmlTag="label"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@context">
                                    Browse Files
                                </MudButton>
                            </ActivatorContent>
                        </MudFileUpload>
                    </div>
                </div>

                @if (selectedFiles.Any())
                {
                    <div class="selected-files mt-2">
                        @foreach (var file in selectedFiles)
                        {
                            <div class="file-item">
                                <i class="material-icons">attach_file</i>
                                <span class="file-name">@file.Name</span>
                                <span class="file-size">@(file.Size / 1024) KB</span>
                            </div>
                        }
                    </div>

                    <style>
                        .selected-files {
                            max-height: 200px;
                            overflow-y: auto;
                            border: 1px solid #e0e0e0;
                            border-radius: 4px;
                            padding: 8px;
                        }

                        .file-item {
                            display: flex;
                            align-items: center;
                            padding: 4px 8px;
                            border-bottom: 1px solid #f0f0f0;
                        }

                        .file-item:last-child {
                            border-bottom: none;
                        }

                        .file-item i {
                            font-size: 18px;
                            color: #666;
                            margin-right: 8px;
                        }

                        .file-name {
                            flex: 1;
                            font-size: 14px;
                            color: #333;
                        }

                        .file-size {
                            font-size: 12px;
                            color: #666;
                            background: #f5f5f5;
                            padding: 2px 6px;
                            border-radius: 3px;
                            margin-left: 8px;
                        }
                    </style>
                }

                <div class="row mb-3">
                    <div class="col-md">
                        <SfDropDownList TValue="int" TItem="AttachmentTypeDto"
                                        @bind-Value="newAttachment.AttachmentTypeId"
                                        DataSource="@attachmentTypes"
                                        Placeholder="Select File Type"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfTextBox Multiline="true"
                                   Placeholder="Description"
                                   FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="newAttachment.Description">
                        </SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Disabled="@(selectedFiles == null || !selectedFiles.Any())">
                            Upload
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog @ref="dlgSectionPreview" Width="800px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          Visible="false">
    <DialogTemplates>
        <Header>Section Preview - @_sectionTitle</Header>
        <Content>
            @((MarkupString)_sectionText)
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="toastObj"></SfToast>

<div class="container">
<div class="row mt-1">
    <div class="col-md" style="text-align: end !important; display: flex; gap: 10px; justify-content: end;">
        @if (isProgress)
        {
            <MudText Typo="Typo.caption" Color="Color.Info">Please Wait...</MudText>
            <MudProgressCircular Size="Size.Small" Color="Color.Info" Indeterminate="true"/>
        }
        else
        {
            <MudButton Style="margin-right:10px;" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                       ButtonType="ButtonType.Button"
                       StartIcon="@Icons.Material.Filled.Save" OnClick="@(() => SaveData())">
                Update
            </MudButton>
            
            <MudButton Style="margin-right:10px;" Size="Size.Small" Variant="Variant.Filled"
                       Color="Color.Success"
                       ButtonType="ButtonType.Button"
                       StartIcon="@Icons.Material.Filled.Send" OnClick="ForwardRequest">
                Forward
            </MudButton>
        }
    </div>
</div>

<!-- Display Memo Title instead of dropdown -->
<div class="row mt-2">
    <div class="col-md">
        <div class="memo-title-display" style="padding: 12px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;">
            <MudText Typo="Typo.h6" Color="Color.Primary">@_memoDto.MemoTitle</MudText>
            <MudText Typo="Typo.caption" Color="Color.Secondary">@_memoDto.MemoCode</MudText>
        </div>
    </div>
</div>

<div class="row mt-2">
    <div class="col-md-9">
        <div class="row">
            <div class="col-md mb-3">
                @foreach (var sec in _memoSectionDTO)
                {
                    <div class="row mt-4">
                        <div class="col-md">
                            <MudText Style="@($"color:{Colors.Indigo.Default};font-weight:700;")"
                                     Color="Color.Primary" Typo="Typo.subtitle2">
                                @sec.TemplateSectionTitle
                            </MudText>
                        </div>
                        <div class="col-md-2" style="text-align:end !important;">
                            @{
                                if (sec.IsRequired == false)
                                {
                                    <MudText Typo="Typo.caption">Ignore Section</MudText>
                                }
                            }
                        </div>
                        <div class="col-md-1" style="text-align:end !important;">
                            @{
                                if (sec.IsRequired == false)
                                {
                                    <SfSwitch OnLabel="Yes" OffLabel="No"
                                              @bind-Checked="sec.IgnoreSection">
                                    </SfSwitch>
                                }
                            }
                        </div>
                        <div class="col-md-1" style="text-align:end !important;">
                            <MudIconButton Icon="@Icons.Material.Filled.QuestionMark"
                                           OnClick="@(() => OpenPopover(sec))" Variant="Variant.Outlined"
                                           Color="Color.Primary" Size="Size.Small"/>
                        </div>
                    </div>
                    @if (sec.IgnoreSection == false)
                    {
                        <div class="row mt-1">
                            <div class="col">
                                <SfRichTextEditor Placeholder="@sec.Placeholder"
                                                  Width="100%"
                                                  @bind-Value="sec.ContentHtml">
                                    <RichTextEditorToolbarSettings Items="@_tools" Type="ToolbarType.Expand"/>
                                    <RichTextEditorEvents
                                        BeforeUploadImage="OnBeforeImageBrowse"
                                        BeforePasteCleanup="OnPasteCleanup">
                                    </RichTextEditorEvents>
                                    <RichTextEditorPasteCleanupSettings Prompt="false"/>
                                </SfRichTextEditor>
                            </div>
                        </div>
                    }
                }
            </div>
        </div>
    </div>
    <div class="col-md-3">
        @if (currentTemplate?.MemoTemplateAttachmentAllowed == true)
        {
            <div class="row mt-4">
                <div class="col-md">
                    <div style="display:flex;gap:5px;justify-content:space-between;align-items:center">
                        <MudText Typo="Typo.subtitle2">Attachments</MudText>
                        <div style="flex:1; height:2px;background-color:gray"></div>
                        <MudFab StartIcon="@Icons.Material.Filled.Add"
                                Color="Color.Primary"
                                Disabled="@(MemoAttachments.Count >= currentTemplate.MemoTemplateAttachmentFileCountAllowed)"
                                OnClick="OpenFileUploadDialog"
                                Size="Size.Small">
                        </MudFab>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md mt-1">
                    <div class="overflow-auto"
                         style="padding-left:10px;padding-top:5px;;height:200px; background-color:white;border-radius:5%">
                        @foreach (var item in MemoAttachments)
                        {
                            <MudStack Spacing="1">
                                <MudText Typo="Typo.caption" Color="Color.Success">
                                    <MudLink Target="_Blank" Href="@AppDataService.ConvertToUrl(item.Path)"
                                             Typo="Typo.caption" Color="Color.Success"
                                             Underline="Underline.None">
                                        @item.Name
                                    </MudLink>
                                    <SfButton CssClass="e-small e-flat e-danger"
                                              IconCss="e-icons e-trash"
                                              OnClick="@(async () => await GetDeleteAttachment(item))"
                                              PreventDefault="true">
                                    </SfButton>
                                </MudText>
                                <MudText Typo="Typo.caption" Style="@($"color:{Colors.Gray.Default};")">
                                    <b>Size:</b> @item.Size bytes, <b>Type:</b> @item.AttachmentType
                                </MudText>
                                <MudSpacer/>
                            </MudStack>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</div>

</div>

<style>
/* Custom list styles for Syncfusion RichTextEditor to avoid CSS conflicts */
.e-richtexteditor .e-rte-content ul,
.e-richtexteditor .e-rte-content ol {
    margin: 0 0 1em 1.5em !important;
    padding: 0 !important;
    list-style-position: inside !important;
}
.e-richtexteditor .e-rte-content ul {
    list-style-type: disc !important;
}
.e-richtexteditor .e-rte-content ol {
    list-style-type: decimal !important;
}
.e-richtexteditor .e-rte-content ul ul,
.e-richtexteditor .e-rte-content ol ul,
.e-richtexteditor .e-rte-content ul ol,
.e-richtexteditor .e-rte-content ol ol {
    margin-left: 1.5em !important;
}
</style>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    [Parameter] public string? strMemoLogId { get; set; }

    private string _userId = "";
    private List<MemoSectionDto> _memoSectionDTO = new();
    private MemoDto _memoDto = new();
    private SfToast? toastObj;
    private bool isProgress;
    private List<MemoAttachmentDto> MemoAttachments = new();
    private SfDialog? dlgFileUpload;
    private readonly List<IBrowserFile> selectedFiles = new();
    private List<AttachmentTypeDto> attachmentTypes = new();
    private FileUploadModel newAttachment = new();
    private TemplateDto? currentTemplate;
    private int memoLogId;
    private int memoId;
    private SfDialog? dlgSectionPreview;
    private string _sectionText = "";
    private string _sectionTitle = "";
    private List<PendingAttachment> PendingAttachments = new();

    private class FileUploadModel
    {
        public int AttachmentTypeId { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    private class PendingAttachment
    {
        public string FileName { get; set; } = "";
        public string TempFilePath { get; set; } = "";
        public int AttachmentTypeId { get; set; }
        public string Description { get; set; } = "";
        public long Size { get; set; }
    }

    private List<ToolbarItemModel> _tools = new()
    {
        new ToolbarItemModel { Command = ToolbarCommand.Bold },
        new ToolbarItemModel { Command = ToolbarCommand.Italic },
        new ToolbarItemModel { Command = ToolbarCommand.Underline },
        new ToolbarItemModel { Command = ToolbarCommand.StrikeThrough },
        new ToolbarItemModel { Command = ToolbarCommand.FontName },
        new ToolbarItemModel { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel { Command = ToolbarCommand.FontColor },
        new ToolbarItemModel { Command = ToolbarCommand.BackgroundColor },
        new ToolbarItemModel { Command = ToolbarCommand.LowerCase },
        new ToolbarItemModel { Command = ToolbarCommand.UpperCase },
        new ToolbarItemModel { Command = ToolbarCommand.SuperScript },
        new ToolbarItemModel { Command = ToolbarCommand.SubScript },
        new ToolbarItemModel { Command = ToolbarCommand.Separator },
        new ToolbarItemModel { Command = ToolbarCommand.Formats },
        new ToolbarItemModel { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel { Command = ToolbarCommand.Outdent },
        new ToolbarItemModel { Command = ToolbarCommand.Indent },
        new ToolbarItemModel { Command = ToolbarCommand.Separator },
        new ToolbarItemModel { Command = ToolbarCommand.CreateLink },
        new ToolbarItemModel { Command = ToolbarCommand.Image },
        new ToolbarItemModel { Command = ToolbarCommand.CreateTable },
        new ToolbarItemModel { Command = ToolbarCommand.Separator },
        new ToolbarItemModel { Command = ToolbarCommand.ClearFormat },
        new ToolbarItemModel { Command = ToolbarCommand.Print },
        new ToolbarItemModel { Command = ToolbarCommand.SourceCode },
        new ToolbarItemModel { Command = ToolbarCommand.FullScreen },
        new ToolbarItemModel { Command = ToolbarCommand.Separator },
        new ToolbarItemModel { Command = ToolbarCommand.Undo },
        new ToolbarItemModel { Command = ToolbarCommand.Redo }
    };

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        // Load attachment types
        attachmentTypes = await AdminService.GetAttachmentTypes();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }

        // Parse memo log ID
        if (!string.IsNullOrEmpty(strMemoLogId) && int.TryParse(strMemoLogId, out var parsedMemoLogId))
        {
            memoLogId = parsedMemoLogId;

            // Get memo data using the log ID
            _memoDto = await WLService.GetMemoByLogId(memoLogId);
            memoId = _memoDto.MemoId;

            if (memoId > 0)
            {
                // Load memo sections and attachments
                _memoSectionDTO = await Service.GetMemoSections(memoId, _memoDto);
                MemoAttachments = await WLService.GetMemoAttachments(memoLogId);
                currentTemplate = await Service.GetMemoTemplate(memoId);

                // Load attachment types based on memo template
                if (_memoDto.MemoTemplateId.HasValue)
                {
                    attachmentTypes = await AdminService.GetAttachmentTypes(_memoDto.MemoTemplateId.Value);
                }
            }
        }
    }

    private async Task OpenPopover(MemoSectionDto tSec)
    {
        _sectionText = tSec.Placeholder;
        _sectionTitle = tSec.TemplateSectionTitle;
        await dlgSectionPreview!.ShowAsync();
    }

    private async Task OpenFileUploadDialog()
    {
        newAttachment = new FileUploadModel();
        selectedFiles.Clear();
        await dlgFileUpload!.ShowAsync();
    }

    private void FileSelected(IReadOnlyList<IBrowserFile> files)
    {
        selectedFiles.Clear();
        foreach (var file in files)
        {
            selectedFiles.Add(file);
        }
        StateHasChanged();
    }

    private async Task HandleFileUpload()
    {
        if (!selectedFiles.Any() || newAttachment.AttachmentTypeId <= 0)
        {
            await ShowToast("Please select a file and attachment type", "e-toast-warning");
            return;
        }

        try
        {
            isProgress = true;
            StateHasChanged();

            foreach (var file in selectedFiles)
            {
                await UploadAttachments(file);
            }

            selectedFiles.Clear();
            newAttachment = new FileUploadModel();
            await dlgFileUpload!.HideAsync();
            await ShowToast("File uploaded successfully", "e-toast-success");
        }
        catch (Exception ex)
        {
            await ShowToast("Error uploading file: " + ex.Message, "e-toast-danger");
        }
        finally
        {
            isProgress = false;
            StateHasChanged();
        }
    }

    private async Task UploadAttachments(IBrowserFile file)
    {
        try
        {
            var random = new Random();
            var randomNumber = random.Next(100000, 999999);
            var newFileName = $"{randomNumber}_{file.Name}";
            var dirPath = $"{Env.WebRootPath}\\Memos\\Attachments";

            if (!Directory.Exists(dirPath))
            {
                Directory.CreateDirectory(dirPath);
            }

            var path = Path.Combine(dirPath, newFileName);
            await using (var stream = file.OpenReadStream(30 * 1024 * 1024))
            await using (var fs = File.Create(path))
            {
                await stream.CopyToAsync(fs);
            }

            var attachmentType = attachmentTypes.FirstOrDefault(at => at.Id == newAttachment.AttachmentTypeId);

            MemoAttachmentDto attachment = new()
            {
                Name = newFileName,
                Type = file.ContentType,
                Path = AppDataService.ConvertToUrl(path),
                Size = file.Size.ToString(),
                AttachmentType = attachmentType?.Title ?? "Unknown",
                Description = newAttachment.Description
            };

            MemoAttachments.Add(attachment);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowToast("Error uploading file: " + ex.Message, "e-toast-danger");
        }
    }

    private async Task GetDeleteAttachment(MemoAttachmentDto mAttach)
    {
        var result = await js.InvokeAsync<bool>("confirm", "Are you sure you want to remove this attachment?");
        if (!result) return;

        try
        {
            // Check if it's a pending attachment
            var pending = PendingAttachments.FirstOrDefault(p => p.FileName == mAttach.Name);
            if (pending != null)
            {
                if (File.Exists(pending.TempFilePath))
                    File.Delete(pending.TempFilePath);
                PendingAttachments.Remove(pending);
            }

            // Remove from UI list
            MemoAttachments.Remove(mAttach);

            // If it's a saved attachment (has a path), delete the physical file
            if (!string.IsNullOrEmpty(mAttach.Path))
            {
                var filePath = Path.Combine(Env.WebRootPath, mAttach.Path.TrimStart('/'));
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            await ShowToast("Error deleting attachment: " + ex.Message, "e-toast-danger");
        }
    }

    private async Task SaveData()
    {
        try
        {
            isProgress = true;
            StateHasChanged();

            // Save memo using the existing SaveMemo method
            var result = await Service.SaveMemo(memoId, _memoDto, _memoSectionDTO, MemoAttachments, new List<MemoApproverDto>(), _userId, "DRAFT", false);

            if (result > 0)
            {
                await ShowToast("Memo updated successfully", "e-toast-success");
            }
            else
            {
                await ShowToast("Error updating memo", "e-toast-danger");
            }
        }
        catch (Exception ex)
        {
            await ShowToast("Error updating memo: " + ex.Message, "e-toast-danger");
        }
        finally
        {
            isProgress = false;
            StateHasChanged();
        }
    }

    private async Task ForwardRequest()
    {
        try
        {
            isProgress = true;
            StateHasChanged();

            // First save the current changes
            await SaveData();

            // Forward the request using the existing PerformForward method
            var result = await WLService.PerformForward(memoLogId);

            if (result == "OK")
            {
                await ShowToast("Request forwarded successfully", "e-toast-success");

                // Navigate back to memos page after a short delay
                await Task.Delay(1500);
                NavMgr.NavigateTo("/memos");
            }
            else
            {
                await ShowToast("Error forwarding request: " + result, "e-toast-danger");
            }
        }
        catch (Exception ex)
        {
            await ShowToast("Error forwarding request: " + ex.Message, "e-toast-danger");
        }
        finally
        {
            isProgress = false;
            StateHasChanged();
        }
    }

    private async Task ShowToast(string message, string cssClass)
    {
        var toastModel = new ToastModel
        {
            Title = cssClass.Contains("success") ? "Success" : cssClass.Contains("warning") ? "Warning" : "Error",
            Content = message,
            ShowProgressBar = true,
            CssClass = cssClass,
            ShowCloseButton = true,
            Timeout = 5000
        };
        await toastObj!.ShowAsync(toastModel);
    }

    private void OnBeforeImageBrowse(ImageUploadingEventArgs args)
    {
        // Handle image upload if needed
        args.Cancel = true; // For now, disable image upload
    }

    private void OnPasteCleanup(PasteCleanupArgs args)
    {
        // Handle paste cleanup if needed
        if (!string.IsNullOrEmpty(args.Value))
        {
            // Remove Word comments and tags
            args.Value = Regex.Replace(args.Value, @"<!--\[if.*?endif\]-->", string.Empty, RegexOptions.Singleline);
            // Remove mso- styles
            args.Value = Regex.Replace(args.Value, @"mso-[^:]+:[^;']+;?", string.Empty, RegexOptions.IgnoreCase);
            // Remove any remaining Word-specific tags
            args.Value = Regex.Replace(args.Value, @"<(o|w|v):[^>]+>.*?<\/(o|w|v):[^>]+>", string.Empty, RegexOptions.Singleline);
        }
    }
}
